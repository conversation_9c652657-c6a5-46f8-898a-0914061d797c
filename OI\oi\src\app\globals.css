@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Local font loading for Aeonik */
@font-face {
  font-family: 'Aeonik-Medium';
  src: url('/fonts/Aeonik-Medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Aeonik-Regular';
  src: url('/fonts/Aeonik-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: 'Aeonik-Regular', sans-serif;
}

body {
  background: #fff;
  color: #000;
  font-family: 'Aeonik-Regular', sans-serif;
}

/* Prevent flash of unstyled content */
.loading-font {
  font-family: 'Aeonik-Medium', sans-serif;
}

/* Smooth number transitions */
.counter-transition {
  font-variant-numeric: tabular-nums;
}

/* Fade in animation for main content */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 1s ease-out forwards;
}