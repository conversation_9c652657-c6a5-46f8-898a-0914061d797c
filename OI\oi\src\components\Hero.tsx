'use client';

import { useRef } from 'react';
import { motion } from 'framer-motion';
import Pool from './Pool';

export default function Hero() {
  const heroRef = useRef<HTMLDivElement>(null);

  return (
    <section 
      ref={heroRef}
      className="relative h-screen w-full overflow-hidden bg-white"
    >
      {/* Background elements */}
      <div className="absolute inset-0 z-0">
        <Pool />
      </div>
      
      {/* Content container */}
      <div className="relative z-10 h-full w-full px-8 sm:px-16 md:px-24 flex flex-col justify-between py-12">
        {/* Top section with logo and menu */}
        <div className="flex justify-between items-start w-full">
          <div className="text-2xl font-medium tracking-tight" style={{ fontFamily: 'Aeonik-Medium, sans-serif' }}>
            LUSION
          </div>
          
          <div className="flex items-center space-x-8">
            <div className="hidden md:block">
              <span className="text-lg tracking-tight" style={{ fontFamily: 'Aeonik-Regular, sans-serif' }}>
                We help brands create digital experiences that connect with their audience
              </span>
            </div>
            
            <button className="flex items-center space-x-2 group">
              <span className="text-lg tracking-tight" style={{ fontFamily: 'Aeonik-Medium, sans-serif' }}>
                LET&apos;S TALK
              </span>
              <div className="relative w-6 h-6 flex items-center justify-center">
                <div className="w-full h-px bg-black absolute transform transition-all duration-300 group-hover:rotate-90"></div>
                <div className="w-full h-px bg-black absolute transform rotate-90 transition-all duration-300 group-hover:rotate-180"></div>
              </div>
            </button>
            
            <button className="flex flex-col items-center justify-center space-y-1 w-8 h-8">
              <span className="w-6 h-px bg-black"></span>
              <span className="w-6 h-px bg-black"></span>
              <span className="text-xs tracking-wide mt-1">MENU</span>
            </button>
          </div>
        </div>
        
        {/* Bottom section with scroll indicator */}
        <div className="flex flex-col items-center justify-center space-y-4">
          <div className="flex flex-col items-center space-y-1">
            <motion.div
              animate={{ y: [0, 8, 0] }}
              transition={{ duration: 1.5, repeat: Infinity }}
              className="w-px h-8 bg-black"
            ></motion.div>
            <span className="text-xs tracking-wide">SCROLL TO EXPLORE</span>
          </div>
        </div>
      </div>
      
      {/* Plus signs in corners */}
      <div className="absolute top-8 right-8 w-6 h-6 flex items-center justify-center opacity-60">
        <div className="w-full h-px bg-black absolute"></div>
        <div className="w-full h-px bg-black absolute transform rotate-90"></div>
      </div>
      
      <div className="absolute bottom-8 left-8 w-6 h-6 flex items-center justify-center opacity-60">
        <div className="w-full h-px bg-black absolute"></div>
        <div className="w-full h-px bg-black absolute transform rotate-90"></div>
      </div>
    </section>
  );
}