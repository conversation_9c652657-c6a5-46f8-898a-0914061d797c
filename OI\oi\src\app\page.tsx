'use client';

import { useState } from 'react';
import Loading from "@/components/Loading";
import Hero from "@/components/Hero";

export default function Home() {
  const [isLoading, setIsLoading] = useState(true);

  if (isLoading) {
    return <Loading onLoadingComplete={() => setIsLoading(false)} />;
  }

  return (
    <div className="bg-white text-black min-h-screen" style={{ fontFamily: 'Aeonik-Regular, sans-serif' }}>
      <Hero />
    </div>
  );
}