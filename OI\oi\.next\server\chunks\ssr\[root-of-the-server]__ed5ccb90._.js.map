{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/Nextjs/Work/OI/oi/src/components/Loading.tsx"], "sourcesContent": ["// components/Loading.tsx\r\nimport React, { useState, useEffect } from 'react';\r\n\r\ninterface LoadingProps {\r\n  onLoadingComplete: () => void;\r\n}\r\n\r\nconst Loading: React.FC<LoadingProps> = ({ onLoadingComplete }) => {\r\n  const [progress, setProgress] = useState(0);\r\n  const [isComplete, setIsComplete] = useState(false);\r\n  const [isZooming, setIsZooming] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const duration = 3000; // 3 seconds total loading time\r\n    const interval = 30; // Update every 30ms for smoother animation\r\n    const steps = duration / interval;\r\n    const increment = 100 / steps;\r\n\r\n    let currentProgress = 0;\r\n\r\n    const timer = setInterval(() => {\r\n      currentProgress += increment;\r\n      \r\n      if (currentProgress >= 100) {\r\n        clearInterval(timer);\r\n        setProgress(100);\r\n        setIsComplete(true);\r\n        \r\n        // Start zoom animation after progress completes\r\n        setTimeout(() => {\r\n          setIsZooming(true);\r\n        }, 300);\r\n        \r\n        // Call onLoadingComplete after zoom animation\r\n        setTimeout(() => {\r\n          onLoadingComplete();\r\n        }, 1300); // 300ms delay + 1000ms zoom animation\r\n        return;\r\n      }\r\n      \r\n      // Add some easing to make it feel more natural\r\n      const eased = easeOutQuart(currentProgress / 100) * 100;\r\n      setProgress(eased);\r\n    }, interval);\r\n\r\n    return () => clearInterval(timer);\r\n  }, [onLoadingComplete]);\r\n\r\n  // Easing function for smooth animation\r\n  const easeOutQuart = (t: number): number => {\r\n    return 1 - Math.pow(1 - t, 4);\r\n  };\r\n\r\n  return (\r\n    <div className={`fixed inset-0 bg-black flex items-center justify-center z-50 transition-all duration-1000 ${\r\n      isZooming ? 'scale-[50] bg-white' : 'scale-100'\r\n    } ${isComplete && !isZooming ? 'opacity-100' : isComplete ? 'opacity-100' : 'opacity-100'}`}>\r\n      <div className=\"relative w-full max-w-lg px-8\">\r\n        {/* Progress Counter */}\r\n        <div className={`absolute bottom-20 left-8 transition-opacity duration-300 ${\r\n          isZooming ? 'opacity-0' : 'opacity-100'\r\n        }`}>\r\n          <span \r\n            className=\"text-white font-medium tracking-tight select-none\"\r\n            style={{ \r\n              fontFamily: 'Aeonik-Medium, sans-serif',\r\n              fontSize: 'clamp(4rem, 12vw, 8rem)',\r\n              lineHeight: '0.8',\r\n              fontFeatureSettings: '\"tnum\" 1'\r\n            }}\r\n          >\r\n            {Math.floor(progress).toString().padStart(3, '0')}\r\n          </span>\r\n        </div>\r\n\r\n        {/* Progress Bar Container */}\r\n        <div className=\"relative w-full h-0.5 bg-gray-700 mx-auto\">\r\n          {/* Progress Bar Fill */}\r\n          <div \r\n            className={`absolute top-0 left-0 h-full bg-white transition-all duration-75 ease-out ${\r\n              isZooming ? 'scale-y-[2000] origin-center' : ''\r\n            }`}\r\n            style={{ \r\n              width: `${progress}%`,\r\n              transformOrigin: 'left center',\r\n              boxShadow: '0 0 10px rgba(255,255,255,0.3)'\r\n            }}\r\n          />\r\n        </div>\r\n\r\n        {/* Loading Text */}\r\n        <div className={`absolute top-20 left-8 transition-opacity duration-300 ${\r\n          isZooming ? 'opacity-0' : 'opacity-100'\r\n        }`}>\r\n          <div \r\n            className=\"text-white text-xs font-normal tracking-widest opacity-50 select-none\"\r\n            style={{ \r\n              fontFamily: 'Aeonik-Regular, sans-serif',\r\n              letterSpacing: '0.2em'\r\n            }}\r\n          >\r\n            LOADING\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Subtle background effect */}\r\n      <div className={`absolute inset-0 overflow-hidden pointer-events-none transition-opacity duration-300 ${\r\n        isZooming ? 'opacity-0' : 'opacity-100'\r\n      }`}>\r\n        <div \r\n          className=\"absolute inset-0 transition-opacity duration-2000\"\r\n          style={{\r\n            background: `linear-gradient(90deg, transparent ${progress - 20}%, rgba(255,255,255,0.03) ${progress}%, transparent ${progress + 20}%)`,\r\n            opacity: progress > 10 ? 1 : 0\r\n          }}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Loading;"], "names": [], "mappings": "AAAA,yBAAyB;;;;;AACzB;;;AAMA,MAAM,UAAkC,CAAC,EAAE,iBAAiB,EAAE;IAC5D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,MAAM,+BAA+B;QACtD,MAAM,WAAW,IAAI,2CAA2C;QAChE,MAAM,QAAQ,WAAW;QACzB,MAAM,YAAY,MAAM;QAExB,IAAI,kBAAkB;QAEtB,MAAM,QAAQ,YAAY;YACxB,mBAAmB;YAEnB,IAAI,mBAAmB,KAAK;gBAC1B,cAAc;gBACd,YAAY;gBACZ,cAAc;gBAEd,gDAAgD;gBAChD,WAAW;oBACT,aAAa;gBACf,GAAG;gBAEH,8CAA8C;gBAC9C,WAAW;oBACT;gBACF,GAAG,OAAO,sCAAsC;gBAChD;YACF;YAEA,+CAA+C;YAC/C,MAAM,QAAQ,aAAa,kBAAkB,OAAO;YACpD,YAAY;QACd,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAkB;IAEtB,uCAAuC;IACvC,MAAM,eAAe,CAAC;QACpB,OAAO,IAAI,KAAK,GAAG,CAAC,IAAI,GAAG;IAC7B;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,0FAA0F,EACzG,YAAY,wBAAwB,YACrC,CAAC,EAAE,cAAc,CAAC,YAAY,gBAAgB,aAAa,gBAAgB,eAAe;;0BACzF,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAW,CAAC,0DAA0D,EACzE,YAAY,cAAc,eAC1B;kCACA,cAAA,8OAAC;4BACC,WAAU;4BACV,OAAO;gCACL,YAAY;gCACZ,UAAU;gCACV,YAAY;gCACZ,qBAAqB;4BACvB;sCAEC,KAAK,KAAK,CAAC,UAAU,QAAQ,GAAG,QAAQ,CAAC,GAAG;;;;;;;;;;;kCAKjD,8OAAC;wBAAI,WAAU;kCAEb,cAAA,8OAAC;4BACC,WAAW,CAAC,0EAA0E,EACpF,YAAY,iCAAiC,IAC7C;4BACF,OAAO;gCACL,OAAO,GAAG,SAAS,CAAC,CAAC;gCACrB,iBAAiB;gCACjB,WAAW;4BACb;;;;;;;;;;;kCAKJ,8OAAC;wBAAI,WAAW,CAAC,uDAAuD,EACtE,YAAY,cAAc,eAC1B;kCACA,cAAA,8OAAC;4BACC,WAAU;4BACV,OAAO;gCACL,YAAY;gCACZ,eAAe;4BACjB;sCACD;;;;;;;;;;;;;;;;;0BAOL,8OAAC;gBAAI,WAAW,CAAC,qFAAqF,EACpG,YAAY,cAAc,eAC1B;0BACA,cAAA,8OAAC;oBACC,WAAU;oBACV,OAAO;wBACL,YAAY,CAAC,mCAAmC,EAAE,WAAW,GAAG,0BAA0B,EAAE,SAAS,eAAe,EAAE,WAAW,GAAG,EAAE,CAAC;wBACvI,SAAS,WAAW,KAAK,IAAI;oBAC/B;;;;;;;;;;;;;;;;;AAKV;uCAEe", "debugId": null}}, {"offset": {"line": 160, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/Nextjs/Work/OI/oi/src/components/Pool.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Canvas } from '@react-three/fiber';\r\nimport { OrbitControls } from '@react-three/drei';\r\n\r\n// Placeholder component for 3D elements\r\nfunction Placeholder3D() {\r\n  return (\r\n    <mesh>\r\n      <boxGeometry args={[2, 2, 2]} />\r\n      <meshStandardMaterial color=\"#f0f0f0\" wireframe />\r\n    </mesh>\r\n  );\r\n}\r\n\r\nexport default function Pool() {\r\n  return (\r\n    <div className=\"absolute inset-0\">\r\n      <Canvas camera={{ position: [0, 0, 5], fov: 75 }}>\r\n        <ambientLight intensity={0.5} />\r\n        <pointLight position={[10, 10, 10]} />\r\n        <Placeholder3D />\r\n        <OrbitControls enableZoom={false} />\r\n      </Canvas>\r\n      \r\n      {/* Overlay gradient */}\r\n      <div className=\"absolute inset-0 bg-gradient-to-b from-transparent to-white opacity-30 pointer-events-none\"></div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,wCAAwC;AACxC,SAAS;IACP,qBACE,8OAAC;;0BACC,8OAAC;gBAAY,MAAM;oBAAC;oBAAG;oBAAG;iBAAE;;;;;;0BAC5B,8OAAC;gBAAqB,OAAM;gBAAU,SAAS;;;;;;;;;;;;AAGrD;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,mMAAA,CAAA,SAAM;gBAAC,QAAQ;oBAAE,UAAU;wBAAC;wBAAG;wBAAG;qBAAE;oBAAE,KAAK;gBAAG;;kCAC7C,8OAAC;wBAAa,WAAW;;;;;;kCACzB,8OAAC;wBAAW,UAAU;4BAAC;4BAAI;4BAAI;yBAAG;;;;;;kCAClC,8OAAC;;;;;kCACD,8OAAC,iKAAA,CAAA,gBAAa;wBAAC,YAAY;;;;;;;;;;;;0BAI7B,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 268, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/Nextjs/Work/OI/oi/src/components/Hero.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useRef } from 'react';\r\nimport { motion } from 'framer-motion';\r\nimport Pool from './Pool';\r\n\r\nexport default function Hero() {\r\n  const heroRef = useRef<HTMLDivElement>(null);\r\n\r\n  return (\r\n    <section \r\n      ref={heroRef}\r\n      className=\"relative h-screen w-full overflow-hidden bg-white\"\r\n    >\r\n      {/* Background elements */}\r\n      <div className=\"absolute inset-0 z-0\">\r\n        <Pool />\r\n      </div>\r\n      \r\n      {/* Content container */}\r\n      <div className=\"relative z-10 h-full w-full px-8 sm:px-16 md:px-24 flex flex-col justify-between py-12\">\r\n        {/* Top section with logo and menu */}\r\n        <div className=\"flex justify-between items-start w-full\">\r\n          <div className=\"text-2xl font-medium tracking-tight\" style={{ fontFamily: 'Aeonik-Medium, sans-serif' }}>\r\n            LUSION\r\n          </div>\r\n          \r\n          <div className=\"flex items-center space-x-8\">\r\n            <div className=\"hidden md:block\">\r\n              <span className=\"text-lg tracking-tight\" style={{ fontFamily: 'Aeonik-Regular, sans-serif' }}>\r\n                We help brands create digital experiences that connect with their audience\r\n              </span>\r\n            </div>\r\n            \r\n            <button className=\"flex items-center space-x-2 group\">\r\n              <span className=\"text-lg tracking-tight\" style={{ fontFamily: 'Aeonik-Medium, sans-serif' }}>\r\n                LET&apos;S TALK\r\n              </span>\r\n              <div className=\"relative w-6 h-6 flex items-center justify-center\">\r\n                <div className=\"w-full h-px bg-black absolute transform transition-all duration-300 group-hover:rotate-90\"></div>\r\n                <div className=\"w-full h-px bg-black absolute transform rotate-90 transition-all duration-300 group-hover:rotate-180\"></div>\r\n              </div>\r\n            </button>\r\n            \r\n            <button className=\"flex flex-col items-center justify-center space-y-1 w-8 h-8\">\r\n              <span className=\"w-6 h-px bg-black\"></span>\r\n              <span className=\"w-6 h-px bg-black\"></span>\r\n              <span className=\"text-xs tracking-wide mt-1\">MENU</span>\r\n            </button>\r\n          </div>\r\n        </div>\r\n        \r\n        {/* Bottom section with scroll indicator */}\r\n        <div className=\"flex flex-col items-center justify-center space-y-4\">\r\n          <div className=\"flex flex-col items-center space-y-1\">\r\n            <motion.div\r\n              animate={{ y: [0, 8, 0] }}\r\n              transition={{ duration: 1.5, repeat: Infinity }}\r\n              className=\"w-px h-8 bg-black\"\r\n            ></motion.div>\r\n            <span className=\"text-xs tracking-wide\">SCROLL TO EXPLORE</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      {/* Plus signs in corners */}\r\n      <div className=\"absolute top-8 right-8 w-6 h-6 flex items-center justify-center opacity-60\">\r\n        <div className=\"w-full h-px bg-black absolute\"></div>\r\n        <div className=\"w-full h-px bg-black absolute transform rotate-90\"></div>\r\n      </div>\r\n      \r\n      <div className=\"absolute bottom-8 left-8 w-6 h-6 flex items-center justify-center opacity-60\">\r\n        <div className=\"w-full h-px bg-black absolute\"></div>\r\n        <div className=\"w-full h-px bg-black absolute transform rotate-90\"></div>\r\n      </div>\r\n    </section>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAEvC,qBACE,8OAAC;QACC,KAAK;QACL,WAAU;;0BAGV,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0HAAA,CAAA,UAAI;;;;;;;;;;0BAIP,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;gCAAsC,OAAO;oCAAE,YAAY;gCAA4B;0CAAG;;;;;;0CAIzG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;4CAAyB,OAAO;gDAAE,YAAY;4CAA6B;sDAAG;;;;;;;;;;;kDAKhG,8OAAC;wCAAO,WAAU;;0DAChB,8OAAC;gDAAK,WAAU;gDAAyB,OAAO;oDAAE,YAAY;gDAA4B;0DAAG;;;;;;0DAG7F,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;;;;;;;;;;;;;kDAInB,8OAAC;wCAAO,WAAU;;0DAChB,8OAAC;gDAAK,WAAU;;;;;;0DAChB,8OAAC;gDAAK,WAAU;;;;;;0DAChB,8OAAC;gDAAK,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;;;;;;;kCAMnD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,GAAG;4CAAC;4CAAG;4CAAG;yCAAE;oCAAC;oCACxB,YAAY;wCAAE,UAAU;wCAAK,QAAQ;oCAAS;oCAC9C,WAAU;;;;;;8CAEZ,8OAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;0BAM9C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;AAIvB", "debugId": null}}, {"offset": {"line": 527, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/Nextjs/Work/OI/oi/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Loading from \"@/components/Loading\";\nimport Hero from \"@/components/Hero\";\n\nexport default function Home() {\n  const [isLoading, setIsLoading] = useState(true);\n\n  if (isLoading) {\n    return <Loading onLoadingComplete={() => setIsLoading(false)} />;\n  }\n\n  return (\n    <div className=\"bg-white text-black min-h-screen\" style={{ fontFamily: 'Aeonik-Regular, sans-serif' }}>\n      <Hero />\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,IAAI,WAAW;QACb,qBAAO,8OAAC,6HAAA,CAAA,UAAO;YAAC,mBAAmB,IAAM,aAAa;;;;;;IACxD;IAEA,qBACE,8OAAC;QAAI,WAAU;QAAmC,OAAO;YAAE,YAAY;QAA6B;kBAClG,cAAA,8OAAC,0HAAA,CAAA,UAAI;;;;;;;;;;AAGX", "debugId": null}}]}